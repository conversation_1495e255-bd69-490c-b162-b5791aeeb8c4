<!DOCTYPE html>
<html>
<head>
    <title>Create Test Image</title>
</head>
<body>
    <canvas id="canvas" width="400" height="400"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 创建一个简单的测试图片
        ctx.fillStyle = '#ff6b6b';
        ctx.fillRect(0, 0, 400, 400);
        
        ctx.fillStyle = '#4ecdc4';
        ctx.fillRect(50, 50, 300, 300);
        
        ctx.fillStyle = '#45b7d1';
        ctx.beginPath();
        ctx.arc(200, 200, 100, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = 'white';
        ctx.font = '30px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('TEST', 200, 210);
        
        // 下载图片
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test-image.png';
            a.click();
            URL.revokeObjectURL(url);
        });
    </script>
</body>
</html>
